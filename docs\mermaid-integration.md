# Mermaid Diagram Integration for System Design

## Overview
CodeCops now automatically generates and renders Mermaid diagrams when users ask system design questions. This enhancement provides visual architecture diagrams alongside textual explanations, making system design interviews more effective.

## Features Implemented

### 1. Enhanced System Design Prompt
The system design prompt (`prompts/system-design.md`) now includes:
- **Mandatory Mermaid diagram generation** for all system design responses
- **Multiple diagram types**: High-level architecture, data flow, and database schemas
- **Styling guidelines** for consistent visual presentation
- **Real-world examples** with proper component naming

### 2. Mermaid Rendering Engine
Added to `llm-response.html`:
- **Mermaid.js library integration** (v10.6.1)
- **Dark theme configuration** matching CodeCops UI
- **Automatic diagram detection** and rendering
- **Error handling** for malformed diagrams

### 3. Visual Enhancements
- **Custom styling** for diagram containers
- **Responsive design** that works in both split and full layouts
- **Professional appearance** with proper spacing and borders

## How It Works

### 1. User Interaction Flow
```mermaid
sequenceDiagram
    participant U as User
    participant AI as AI Assistant
    participant R as Renderer
    participant M as Mermaid Engine
    
    U->>AI: "Design a chat system"
    AI->>AI: Apply system-design prompt
    AI->>U: Response with Mermaid code
    R->>R: Detect ```mermaid blocks
    R->>M: Render diagram
    M->>R: SVG diagram
    R->>U: Display visual + text
```

### 2. Diagram Types Generated

#### High-Level Architecture
```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web App]
        MOB[Mobile App]
    end
    
    subgraph "Backend Services"
        API[API Gateway]
        AUTH[Auth Service]
        CHAT[Chat Service]
    end
    
    subgraph "Data Layer"
        DB[Database]
        CACHE[Redis Cache]
    end
    
    WEB --> API
    MOB --> API
    API --> AUTH
    API --> CHAT
    CHAT --> DB
    CHAT --> CACHE
    
    style API fill:#ff6b6b
    style CHAT fill:#4ecdc4
    style DB fill:#96ceb4
```

#### Data Flow Diagrams
```mermaid
sequenceDiagram
    participant C as Client
    participant G as Gateway
    participant S as Service
    participant D as Database
    
    C->>G: Send Message
    G->>S: Route Request
    S->>D: Store Message
    D-->>S: Confirm
    S-->>G: Success
    G-->>C: Message Sent
```

#### Database Schemas
```mermaid
erDiagram
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ CHANNELS : joins
    CHANNELS ||--o{ MESSAGES : contains
    
    USERS {
        bigint id PK
        varchar username UK
        varchar email UK
        timestamp created_at
    }
    
    MESSAGES {
        bigint id PK
        bigint user_id FK
        bigint channel_id FK
        text content
        timestamp created_at
    }
```

## Implementation Details

### 1. Prompt Enhancement
The system design prompt now includes specific instructions:
- Always include at least one Mermaid diagram
- Use appropriate diagram types for different scenarios
- Include realistic component names and connections
- Apply consistent styling with colors

### 2. Frontend Integration
- **Mermaid.js** automatically detects code blocks with `language-mermaid`
- **Custom CSS** ensures diagrams match the dark theme
- **Error handling** gracefully falls back to code display if rendering fails
- **Responsive design** works in both split and full layout modes

### 3. Styling Configuration
```javascript
mermaid.initialize({
    startOnLoad: false,
    theme: 'dark',
    themeVariables: {
        primaryColor: '#374151',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#6b7280',
        lineColor: '#6b7280',
        background: '#111827'
    }
});
```

## Usage Examples

### Example 1: Chat System Design
**User Input**: "Design a real-time chat system for 1 million users"

**AI Response** (includes):
1. Clarifying questions about requirements
2. Capacity estimation with real numbers
3. **High-level architecture diagram** (Mermaid)
4. **Data flow sequence diagram** (Mermaid)
5. **Database schema diagram** (Mermaid)
6. Scaling strategies and bottleneck solutions

### Example 2: E-commerce Platform
**User Input**: "Design an e-commerce platform like Amazon"

**AI Response** (includes):
1. Functional requirements breakdown
2. **Microservices architecture diagram**
3. **Payment flow sequence diagram**
4. **Product catalog schema**
5. Performance metrics and monitoring

## Benefits

### For Interview Preparation
- **Visual learning**: Diagrams help understand complex architectures
- **Professional presentation**: Industry-standard diagram formats
- **Complete coverage**: Both visual and textual explanations
- **Real-world context**: Actual system examples and numbers

### For System Understanding
- **Clear component relationships**: Visual connections between services
- **Data flow visualization**: Step-by-step process flows
- **Schema relationships**: Database design patterns
- **Scaling patterns**: Visual representation of growth strategies

## Technical Notes

### Browser Compatibility
- Uses modern JavaScript features (async/await)
- Requires modern browsers with SVG support
- Graceful fallback to code blocks if Mermaid fails

### Performance
- Diagrams render asynchronously to avoid blocking UI
- Lazy loading prevents performance issues with multiple diagrams
- Optimized for CodeCops' stealth requirements

### Customization
- Diagram themes can be modified in the Mermaid configuration
- CSS styles can be adjusted for different visual preferences
- Additional diagram types can be added by extending the prompt

## 🔍 Enhanced Zoom Functionality

### Zoom Controls
Each Mermaid diagram now includes interactive zoom controls:

#### **Control Buttons**
- **➖ Zoom Out**: Decrease diagram size (minimum 30%)
- **➕ Zoom In**: Increase diagram size (maximum 500%)
- **🏠 Reset**: Return to 100% zoom level
- **⊞ Fit**: Auto-fit diagram to container width

#### **Keyboard Shortcuts**
- **Ctrl + Plus (+)**: Zoom in
- **Ctrl + Minus (-)**: Zoom out
- **Ctrl + 0**: Reset to 100%

#### **Mouse Controls**
- **Ctrl + Mouse Wheel**: Zoom in/out smoothly
- **Scroll**: Navigate within zoomed diagrams

### Zoom Features

#### **Smart Auto-Fit**
- Large diagrams automatically fit to container on load
- Prevents horizontal scrolling for better readability
- Maintains aspect ratio

#### **Smooth Transitions**
- CSS transitions for smooth zoom animations
- Visual feedback with zoom percentage display
- Hover effects for better user experience

#### **Responsive Design**
- Works in both split and full layout modes
- Adapts to different window sizes
- Maintains functionality in stealth mode

#### **Visual Indicators**
- **Zoom Level Display**: Shows current zoom percentage
- **Hover Hints**: "Ctrl+Wheel to zoom" tooltip
- **Button States**: Disabled states at min/max zoom

### Usage Examples

#### **Small Diagrams**
For simple flowcharts or small architectures:
- Default 100% zoom is usually sufficient
- Use zoom in (+) for detailed inspection
- Fit button ensures optimal viewing

#### **Complex System Architectures**
For large enterprise systems:
- Auto-fit activates on load for overview
- Zoom in to examine specific components
- Scroll to navigate different sections

#### **Sequence Diagrams**
For detailed interaction flows:
- Zoom out for full timeline view
- Zoom in for message details
- Horizontal scrolling for long sequences

### Technical Implementation

#### **CSS Transform Scaling**
```css
.mermaid-wrapper {
    transform: scale(1.25); /* 125% zoom */
    transform-origin: top left;
    transition: transform 0.3s ease;
}
```

#### **Zoom Range**
- **Minimum**: 30% (0.3x scale)
- **Maximum**: 500% (5.0x scale)
- **Step Size**: 25% (0.25x increments)
- **Default**: 100% (1.0x scale)

#### **Container Management**
- Overflow handling for zoomed content
- Custom scrollbars for better aesthetics
- Focus management for keyboard shortcuts

### Accessibility Features

#### **Keyboard Navigation**
- All zoom functions accessible via keyboard
- Focus indicators for interactive elements
- Tab navigation through controls

#### **Visual Feedback**
- Clear zoom level indicators
- Disabled state styling
- Hover effects for better usability

#### **Error Handling**
- Graceful fallback to code blocks if Mermaid fails
- Zoom controls only appear after successful rendering
- No zoom controls for failed diagrams
