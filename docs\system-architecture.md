# CodeCops System Architecture Documentation

## Overview
CodeCops is an AI-powered desktop application built with Electron that provides stealth interview assistance through real-time OCR, speech recognition, and AI-powered responses.

## System Architecture Diagrams

### 1. High-Level System Architecture

```mermaid
graph TB
    subgraph "User Interface Layer"
        MW[Main Window]
        CW[Chat Window]
        LW[LLM Response Window]
        SW[Settings Window]
    end
    
    subgraph "Main Process (Electron)"
        MP[Main Process]
        WM[Window Manager]
        SM[Session Manager]
        PL[Prompt Loader]
    end
    
    subgraph "Core Services"
        OCR[OCR Service]
        SPEECH[Speech Service]
        LLM[LLM Service]
        LOG[Logger Service]
        CFG[Config Service]
    end
    
    subgraph "External APIs"
        AZURE[Azure Speech API]
        GEMINI[Google Gemini API]
        TESSERACT[Tesseract OCR]
    end
    
    subgraph "System Integration"
        GS[Global Shortcuts]
        SC[Screen Capture]
        MIC[Microphone Access]
        FS[File System]
    end
    
    MW --> MP
    CW --> MP
    LW --> MP
    SW --> MP
    
    MP --> WM
    MP --> SM
    MP --> PL
    
    WM --> OCR
    WM --> SPEECH
    WM --> LLM
    
    OCR --> TESSERACT
    OCR --> SC
    SPEECH --> <PERSON><PERSON><PERSON><PERSON>
    SPEECH --> MIC
    LLM --> GEMINI
    
    MP --> GS
    MP --> LOG
    MP --> CFG
    
    SM --> FS
    PL --> FS
    
    style MP fill:#ff6b6b
    style WM fill:#4ecdc4
    style SM fill:#45b7d1
    style OCR fill:#96ceb4
    style SPEECH fill:#feca57
    style LLM fill:#ff9ff3
```

### 2. Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant MW as Main Window
    participant MP as Main Process
    participant WM as Window Manager
    participant OCR as OCR Service
    participant SPEECH as Speech Service
    participant LLM as LLM Service
    participant SM as Session Manager
    
    U->>MW: Trigger Screenshot (Ctrl+Shift+S)
    MW->>MP: Global Shortcut Event
    MP->>WM: showLLMLoading()
    MP->>OCR: captureAndProcess()
    OCR->>OCR: captureScreenshot()
    OCR->>OCR: performOCR()
    OCR->>MP: Return extracted text
    MP->>SM: addOCREvent()
    MP->>LLM: processWithLLM()
    LLM->>LLM: buildGeminiRequest()
    LLM-->>MP: AI Response
    MP->>SM: addModelResponse()
    MP->>WM: broadcastToAllWindows()
    WM->>MW: Display Response
```

### 3. Speech Recognition Flow

```mermaid
flowchart TD
    A[User Activates Speech] --> B[Speech Service Start]
    B --> C[Initialize Azure Speech Config]
    C --> D[Create Audio Stream]
    D --> E[Start Microphone Capture]
    E --> F[Audio Data Processing]
    F --> G{Audio Detected?}
    G -->|Yes| H[Send to Azure Speech API]
    G -->|No| F
    H --> I[Receive Transcription]
    I --> J[Emit Transcription Event]
    J --> K[Session Manager Stores]
    K --> L[Broadcast to Windows]
    L --> M[Auto-process with LLM]
    M --> N[Display AI Response]
    
    style A fill:#feca57
    style H fill:#ff6b6b
    style N fill:#96ceb4
```

### 4. Window Management Architecture

```mermaid
graph LR
    subgraph "Window Manager"
        WM[Window Manager Core]
        WT[Window Tracker]
        SS[Stealth System]
        WB[Window Binding]
    end

    subgraph "Window Types"
        MW[Main Window<br/>Control Panel]
        CW[Chat Window<br/>Transcription Display]
        LW[LLM Response Window<br/>AI Responses]
        SW[Settings Window<br/>Configuration]
    end

    subgraph "Stealth Features"
        AOT[Always On Top]
        IGN[Ignore Mouse Events]
        TRANS[Transparency]
        HIDE[Screen Share Hide]
    end

    WM --> MW
    WM --> CW
    WM --> LW
    WM --> SW

    WM --> WT
    WM --> SS
    WM --> WB

    SS --> AOT
    SS --> IGN
    SS --> TRANS
    SS --> HIDE

    style WM fill:#4ecdc4
    style SS fill:#ff6b6b
    style MW fill:#96ceb4
    style CW fill:#feca57
    style LW fill:#ff9ff3
    style SW fill:#45b7d1
```

### 5. Data Flow Architecture

```mermaid
flowchart TB
    subgraph "Input Sources"
        KB[Keyboard Shortcuts]
        MIC[Microphone Input]
        SCREEN[Screen Capture]
        UI[UI Interactions]
    end

    subgraph "Processing Layer"
        OCR[OCR Processing]
        STT[Speech-to-Text]
        LLM[LLM Processing]
    end

    subgraph "Session Management"
        SM[Session Manager]
        MEM[Memory Store]
        HIST[History Optimization]
    end

    subgraph "Output Layer"
        CHAT[Chat Display]
        RESP[Response Window]
        LOG[Logging System]
        NOTIF[Notifications]
    end

    KB --> OCR
    MIC --> STT
    SCREEN --> OCR
    UI --> LLM

    OCR --> SM
    STT --> SM
    LLM --> SM

    SM --> MEM
    SM --> HIST

    SM --> CHAT
    SM --> RESP
    SM --> LOG
    SM --> NOTIF

    style SM fill:#45b7d1
    style MEM fill:#96ceb4
    style LLM fill:#ff9ff3
```

### 6. Skill-Based Processing Flow

```mermaid
flowchart TD
    A[User Input] --> B{Input Type?}
    B -->|Speech| C[Speech Recognition]
    B -->|Screenshot| D[OCR Processing]
    B -->|Chat| E[Direct Input]

    C --> F[Session Manager]
    D --> F
    E --> F

    F --> G[Determine Active Skill]
    G --> H{Skill Type?}

    H -->|Programming| I[Programming Prompt]
    H -->|DSA| J[DSA Prompt]
    H -->|System Design| K[System Design Prompt]
    H -->|Behavioral| L[Behavioral Prompt]
    H -->|DevOps| M[DevOps Prompt]
    H -->|Data Science| N[Data Science Prompt]
    H -->|Sales| O[Sales Prompt]
    H -->|Negotiation| P[Negotiation Prompt]
    H -->|Presentation| Q[Presentation Prompt]

    I --> R[Prompt Loader]
    J --> R
    K --> R
    L --> R
    M --> R
    N --> R
    O --> R
    P --> R
    Q --> R

    R --> S[LLM Service]
    S --> T[Gemini API]
    T --> U[AI Response]
    U --> V[Session Storage]
    V --> W[Display Response]

    style G fill:#feca57
    style R fill:#96ceb4
    style S fill:#ff9ff3
    style T fill:#ff6b6b
```

### 7. Error Handling and Fallback Flow

```mermaid
flowchart TD
    A[Service Request] --> B{Service Available?}
    B -->|Yes| C[Execute Request]
    B -->|No| D[Check Fallback Options]

    C --> E{Request Successful?}
    E -->|Yes| F[Return Result]
    E -->|No| G[Log Error]

    G --> H{Retry Available?}
    H -->|Yes| I[Increment Retry Count]
    H -->|No| J[Use Fallback Method]

    I --> K{Max Retries?}
    K -->|No| C
    K -->|Yes| J

    D --> L{Fallback Enabled?}
    L -->|Yes| M[Execute Fallback]
    L -->|No| N[Return Error]

    J --> M
    M --> O{Fallback Success?}
    O -->|Yes| P[Return Fallback Result]
    O -->|No| N

    F --> Q[Success Response]
    P --> R[Fallback Response]
    N --> S[Error Response]

    style G fill:#ff6b6b
    style M fill:#feca57
    style Q fill:#96ceb4
    style R fill:#45b7d1
    style S fill:#ff6b6b
```

### 8. IPC Communication Architecture

```mermaid
sequenceDiagram
    participant R as Renderer Process
    participant P as Preload Script
    participant M as Main Process
    participant S as Services
    participant API as External APIs

    R->>P: User Action (e.g., take screenshot)
    P->>M: IPC Handle Call
    M->>S: Service Method Call
    S->>API: External API Request
    API-->>S: API Response
    S-->>M: Service Response
    M->>M: Process & Store in Session
    M->>R: Broadcast to All Windows
    R->>R: Update UI

    Note over R,API: Secure IPC with Context Isolation
    Note over M,S: Service Layer Abstraction
    Note over S,API: External API Integration
```

### 9. Security and Stealth Architecture

```mermaid
graph TB
    subgraph "Stealth Layer"
        SL[Stealth Controller]
        WH[Window Hiding]
        PT[Process Title Masking]
        IC[Icon Camouflage]
    end

    subgraph "Security Layer"
        CI[Context Isolation]
        NI[Node Integration Disabled]
        CSP[Content Security Policy]
        SEC[Secure IPC]
    end

    subgraph "Detection Avoidance"
        SSA[Screen Share Avoidance]
        WDA[Window Detection Avoidance]
        PDA[Process Detection Avoidance]
    end

    subgraph "Data Protection"
        MEM[Memory Encryption]
        TMP[Temp File Cleanup]
        LOG[Secure Logging]
        CFG[Config Protection]
    end

    SL --> WH
    SL --> PT
    SL --> IC

    WH --> SSA
    PT --> PDA
    IC --> WDA

    CI --> SEC
    NI --> SEC
    CSP --> SEC

    MEM --> TMP
    TMP --> LOG
    LOG --> CFG

    style SL fill:#ff6b6b
    style CI fill:#96ceb4
    style SSA fill:#feca57
    style MEM fill:#45b7d1
```

## Technical Specifications

### Core Technologies
- **Framework**: Electron (Node.js + Chromium)
- **Language**: JavaScript (ES6+)
- **OCR Engine**: Tesseract.js
- **Speech Recognition**: Azure Cognitive Services
- **AI/LLM**: Google Gemini API
- **UI Framework**: HTML5 + CSS3 + Vanilla JavaScript

### Key Features
1. **Real-time OCR**: Screenshot capture and text extraction
2. **Speech Recognition**: Continuous voice-to-text conversion
3. **AI Integration**: Context-aware responses using Gemini
4. **Stealth Mode**: Invisible to screen sharing applications
5. **Multi-skill Support**: Specialized prompts for different interview types
6. **Session Management**: Conversation history and context preservation
7. **Global Shortcuts**: System-wide keyboard shortcuts
8. **Window Management**: Multiple specialized windows with stealth features

### Performance Characteristics
- **OCR Processing**: ~2-5 seconds per screenshot
- **Speech Recognition**: Real-time streaming
- **LLM Response**: ~3-10 seconds depending on complexity
- **Memory Usage**: ~100-200MB typical
- **CPU Usage**: Low when idle, moderate during processing
